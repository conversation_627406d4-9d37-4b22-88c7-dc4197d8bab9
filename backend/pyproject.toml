[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "backend"
version = "1.0.0"
description = "E-commerce Integration Hub Backend API"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "E-commerce Hub Team", email = "<EMAIL>"},
]
keywords = ["fastapi", "ecommerce", "shopify", "woocommerce", "api"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.12.1",
    "psycopg2-binary>=2.9.9",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "pydantic[email]>=2.5.0",
    "pydantic-settings>=2.1.0",
    "httpx>=0.25.2",
    "python-dotenv>=1.0.0",
    "darts[torch]>=0.29.0",
    "pandas>=1.5.0",
    "numpy>=1.21.0",
    "scikit-learn>=1.0.0",
    "torch>=2.7.1",
    "torchvision>=0.22.1",
]

[project.optional-dependencies]
gcp = [
    "cloud-sql-python-connector",
    "google-cloud-secret-manager",
]
shopify = [
    "ShopifyAPI>=12.0.0",
]
woocommerce = [
    "WooCommerce>=3.0.0",
]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
]
all = [
    "backend[gcp,shopify,woocommerce,dev]",
]

[project.urls]
Homepage = "https://github.com/your-org/ecommerce-hub"
Documentation = "https://github.com/your-org/ecommerce-hub/docs"
Repository = "https://github.com/your-org/ecommerce-hub.git"
Issues = "https://github.com/your-org/ecommerce-hub/issues"

[project.scripts]
ecommerce-hub = "main:app"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.mypy]
python_version = "3.11"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/.venv/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.hatch.build.targets.wheel]
packages = ["."]
